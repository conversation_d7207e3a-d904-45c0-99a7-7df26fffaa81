.App {
  min-height: 100vh;
  padding: 20px 0;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

.status {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.result-item {
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item strong {
  color: #495057;
  margin-right: 10px;
}

.prediction {
  font-size: 1.2rem;
  font-weight: 600;
  margin-left: 10px;
  padding: 4px 12px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 20px;
  display: inline-block;
}

.examples {
  display: grid;
  gap: 20px;
}

.example-group {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.example-group h4 {
  margin-bottom: 15px;
  color: #495057;
}

.example-btn {
  background: white;
  border: 2px solid #dee2e6;
  padding: 10px 16px;
  margin: 5px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.example-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.footer {
  text-align: center;
  margin-top: 40px;
  color: white;
  opacity: 0.8;
}

.footer p {
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .card {
    padding: 16px;
  }
  
  .example-btn {
    display: block;
    width: 100%;
    margin: 5px 0;
  }
}

/* Animation for results */
.result-card {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Confidence bar animation */
.confidence-fill {
  animation: fillBar 1s ease-out;
}

@keyframes fillBar {
  from {
    width: 0%;
  }
}

/* Loading animation improvements */
.loading {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Model Selection Styles */
.model-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.model-option {
  position: relative;
  border-radius: 8px;
  border: 2px solid #e1e5e9;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.model-option:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.model-option.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.model-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.model-label {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  cursor: pointer;
  margin: 0;
  font-weight: normal;
}

.model-option.selected .model-label {
  color: white;
}

.model-emoji {
  font-size: 18px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.model-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.model-name {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.1;
}

.model-description {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.1;
}

.model-option.selected .model-description {
  opacity: 0.9;
}

/* Responsive adjustments for model selection */
@media (max-width: 768px) {
  .model-label {
    padding: 8px 12px;
  }
  
  .model-emoji {
    font-size: 16px;
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  
  .model-name {
    font-size: 13px;
  }
  
  .model-description {
    font-size: 11px;
  }
}
